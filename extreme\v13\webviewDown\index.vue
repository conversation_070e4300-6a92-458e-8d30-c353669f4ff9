<template>
  <view class="container">
    <!--		<view class="cover" v-if="show">当前为第三方网页，请注意核实信息谨防上当受骗</view>-->
    <web-view :src="url" class="wb-view"></web-view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'

export default {
  name: 'webviewDown',

  data() {
    return {
      show: true,
      url: '',
      form: {}
    }
  },

  onLoad({ param, src }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
      this.url = decodeURIComponent(src)
    }

    setTimeout(() => {
      this.show = false
    }, 3000)
  },

  methods: {
    navigateToEnd() {
      const path = '/extreme/v13/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 14rpx 32rpx;
    left: 0;
    top: 0;
    background: #ffedea;
    font-size: 24rpx;
    color: #ff5449;
    text-align: center;
  }
}
</style>
