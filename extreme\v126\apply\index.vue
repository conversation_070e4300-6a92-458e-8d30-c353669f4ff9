<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatAmount, parseAmount } from '@/utils/amount'
import { applyFormProduct } from '@/apis/common-2'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'ApplyIndex',
  components: {
    Declaration,
    Header,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        monthIndex: 0,
        demandAmount: '50000'
      },
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      activeForm: '',
      productList: [],
      monthlyPay: '',
      agreementList: [],
      productPopupData: {}
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.updateAmountUI()
    this.getFlowData()
    this.fetchProtocol()
  },

  methods: {
    async fetchProtocol() {
      this.agreementList = await getAgreements(this.vuex_templateConfig.agreementKeyApply)
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickProductItem(product) {
      this.productPopupData = product
      this.$refs.productPopup.open()
    },

    clickApply() {
      throttle(this.applyHandler)
    },

    applyHandler() {
      const selectedProducts = this.productList.filter((item) => item.uiChecked)
      if (selectedProducts.length === 0) {
        uni.showToast({
          title: '请选择产品',
          icon: 'none'
        })
        return
      }

      this.applyProduct()
    },

    getApplyParams() {
      const params = {
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      }

      params.applyProduct = this.productList.filter((item) => item.uiChecked)
      return params
    },

    async applyProduct() {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const params = this.getApplyParams()
      await applyFormProduct(params)

      uni.hideLoading()

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v126/apply/index',
        wechat: '/extreme/v126/qw/index',
        overloan: '/extreme/v126/applyOther/index',
        end: '/extreme/v126/download/index',
        halfapi: '/extreme/v126/webviewDown/index',
        wechat_official_account: '/extreme/v126/wechatOfficialAccount/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    demandAmountBlur() {
      const amount = parseInt(this.form.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = '200000'
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = amount
      this.updateAmountUI()
    },

    updateAmountUI() {
      this.form.demandAmount = formatAmount(this.form.demandAmount)
      this.computedMonthPay()
    },

    demandAmountFocus() {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    clickEditAmount() {
      this.$refs.amountInput.$el.querySelector('input').focus()
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    monthPickerChange({ detail }) {
      this.form.monthIndex = detail.value
      this.updateAmountUI()
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async getFlowData() {
      const flowData = getFlowData('offline')
      if (flowData) {
        flowData.productList.forEach((product) => {
          product.uiChecked = true
          // 随机生成匹配率（95～99）
          product.matchingDegree = Math.floor(Math.random() * 5 + 95)
        })
        this.productList = flowData.productList
        this.form.appUserId = flowData.appUserId
        this.form.channelId = flowData.channelId
      }
    }
  }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <view class="page-content">
        <Header @click.native="navigateToIndex" />

        <view class="header">
          <view class="header-title">
            <view class="header-title-text">
              <view class="header-title-icon"></view>
              <view>资方匹配成功</view>
            </view>
            <view class="header-title-subtext">请选择借款期数/借款金额</view>
          </view>
          <view class="tag">
            <view class="tag-number">1000</view>
            <view class="tag-gap-text">人</view>
            已放款
          </view>
        </view>

        <view class="card amount-input-container">
          <view class="amount-title">最高可借(元）</view>
          <view class="input-container">
            <view class="amount-symbol">￥</view>
            <input
              class="amount-input"
              type="text"
              value="20,0000"
              v-model="form.demandAmount"
              @blur="demandAmountBlur"
              @focus="demandAmountFocus"
              ref="amountInput"
            />
            <view class="edit-amount-btn" @click="clickEditAmount">修改金额</view>
          </view>
          <view class="amount-tips">该额度为授信金额，您可根据实际需求金额提现</view>
        </view>

        <view
          class="borrowing-options card"
          :class="{ activeForm: activeForm === 'borrowing' }"
          @click="setActiveForm('borrowing')"
        >
          <view style="padding: 0 30rpx 30rpx" class="tips-text">最长借多久</view>
          <view class="option term" style="padding-top: 0">
            <view class="label">申请期数</view>
            <picker
              range-key="label"
              :range="monthRange"
              :value="form.monthIndex"
              @change="monthPickerChange"
            >
              <view class="value">
                <view>{{ monthRange[this.form.monthIndex].value }}个月</view>
                <view class="recommend" v-if="monthRange[this.form.monthIndex].value === 12"
                  >高通过率</view
                >
                <uni-icons
                  style="margin-left: 15rpx"
                  color="#A2A3A5"
                  type="right"
                  size="16"
                ></uni-icons>
              </view>
            </picker>
          </view>
          <view class="item-line"></view>
          <view class="option repayment-method">
            <view class="label">还款计划</view>
            <view class="value">
              <view class="repayment-amount">
                每月约应还
                <text class="amount-number">￥{{ monthlyPay }}</text>
              </view>
              <!--          <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view>-->
            </view>
          </view>
          <view class="item-line"></view>
          <view class="option coupon" style="padding-bottom: 0">
            <view class="label">年利率</view>
            <view class="value">
              <view class="annual-interest-rate">12%起</view>
              <view class="limited-time-offer">限时优惠</view>
            </view>
          </view>
          <view style="padding: 15rpx 30rpx 0rpx" class="tips-text"
            >实际贷款利息及放款金额以最终审批为准</view
          >
        </view>

        <view
          class="product-container card"
          :class="{ activeForm: activeForm === 'product' }"
          @click="setActiveForm('product')"
        >
          <view class="product-count"
            >已匹配
            <view class="product-count-length">{{ productList.length }}</view>
            家机构，通过率预估
            <view class="highlight">98%</view>
          </view>
          <view class="product-list">
            <view v-for="(product, index) in productList" :key="index">
              <view class="product-list-item" @click.stop="clickProductItem(product)">
                <view class="product-info">
                  <image :src="product.logo" class="product-icon" />
                  <view class="product-info">
                    <view class="product-name">{{ product.productName }}</view>
                    <view class="product-line-col"></view>
                    <view class="product-matching-rate">{{ product.matchingDegree }}%</view>
                    <view class="product-expand"></view>
                  </view>
                </view>
                <view class="product-checkbox">
                  <view
                    class="product-checkbox-item"
                    :class="{ checked: product.uiChecked }"
                    @click.stop="product.uiChecked = !product.uiChecked"
                  ></view>
                  <view class="speech-bubble" v-if="!product.uiChecked">
                    <view class="icon-good"></view>
                    通过率+3%
                  </view>
                </view>
              </view>

              <view class="item-line" v-if="index !== productList.length - 1"></view>
            </view>
          </view>
        </view>

        <Declaration />
      </view>
      <view class="page-footer">
        <view class="agreement">
          <view class="agreement-text">
            我已阅读并同意
            <text
              class="name"
              v-for="item in agreementList"
              :key="item.protocolId"
              @click="clickAgreement(item)"
            >
              《{{ item.name }}》
            </text>
          </view>
        </view>
        <view class="apply-btn" @click="clickApply"> 确认申请 </view>
      </view>

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <view class="agreement-container">
            <view v-for="agreement in agreementList" :key="agreement.protocolId">
              <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
              <view class="agreement-content">
                <div v-html="agreement.content"></div>
              </view>
            </view>
          </view>
          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickApply">同意并继续</view>
          </view>
        </view>
      </uni-popup>

      <uni-popup
        background-color="#fff"
        ref="productPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="product-popup">
          <view class="institution">机构信息</view>
          <view class="product">
            <view class="product-content">
              <view class="product-info">
                <image :src="productPopupData.logo" class="product-icon" />
                <view class="product-name">{{ productPopupData.productName }}</view>
                <view class="icon-hot"></view>
                <view class="icon-diamond-selection"></view>
              </view>
              <view class="product-feature">
                <view class="product-feature-item">
                  <view class="product-feature-value highlight">{{
                    productPopupData.loanableFundsBig | toThousandFilter
                  }}</view>
                  <view class="product-feature-label">最高额度(元)</view>
                </view>
                <view class="product-feature-item">
                  <view class="product-feature-value">{{ productPopupData.putinPush }}</view>
                  <view class="product-feature-label">放款人数</view>
                </view>
              </view>
            </view>
            <view class="product-checkbox">
              <view
                class="product-checkbox-item"
                :class="{ checked: productPopupData.uiChecked }"
                @click.stop="productPopupData.uiChecked = !productPopupData.uiChecked"
              ></view>
              <view class="speech-bubble" v-if="!productPopupData.uiChecked">
                <view class="icon-good"></view>
                通过率+3%
              </view>
            </view>
          </view>
        </view>
      </uni-popup>

      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '@/theme/v126/export/apply';
</style>
