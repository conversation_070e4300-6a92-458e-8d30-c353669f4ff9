<template>
  <view class="page-container">
    <!-- 使用Banner组件 -->
    <dyt-qw-banner />

    <!-- 使用InfoSection组件 -->
    <dyt-qw-info-section />

    <!-- 使用Footer组件 -->
    <dyt-qw-footer @contact="handleContact" @cancel="handleCancel" />
  </view>
</template>
<script>
import DytQwBanner from '@/components/dai-yue-tong/qw/Banner.vue'
import DytQwInfoSection from '@/components/dai-yue-tong/qw/InfoSection.vue'
import DytQwFooter from '@/components/dai-yue-tong/qw/Footer.vue'

export default {
  name: 'Qw',
  components: {
    DytQwBanner,
    DytQwInfoSection,
    DytQwFooter
  },
  methods: {
    handleContact() {
      // 处理立即联系按钮点击事件
      console.log('联系按钮被点击')
    },
    handleCancel() {
      // 处理取消按钮点击事件
      console.log('取消按钮被点击')
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  padding-top: 52rpx;
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/c4b684c6fff9467caf389319901cebdc.png);
  background-size: 100% 1250rpx;
  background-repeat: no-repeat;
}
</style>
