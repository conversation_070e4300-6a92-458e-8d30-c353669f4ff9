<template>
  <view class="apply-page">
    <!-- 贷款金额选择区域 -->
    <loan-amount-section
      :loan-amount="loanAmount"
      @update:loan-amount="updateLoanAmount"
      @input-focus="onInputFocus"
      @input-blur="onInputBlur"
      @set-amount="setAmount"
    />

    <!-- 产品列表 -->
    <selectable-product-list
      :product-list="productList"
      :default-selected-all="true"
      @selection-change="handleSelectionChange"
    />

    <!-- 底部申请区域 -->
    <apply-footer
      :selected-count="selectedProducts.length"
      @apply="onProductsApply"
      @open-privacy="openPrivacyPolicy"
      @open-agreement="openUserAgreement"
    />
  </view>
</template>

<script>
import LoanAmountSection from '@/components/dai-yue-tong/index/LoanAmountSection.vue'
import SelectableProductList from '@/components/dai-yue-tong/apply/SelectableProductList.vue'
import ApplyFooter from '@/components/dai-yue-tong/apply/ApplyFooter.vue'

export default {
  name: 'ApplyPage',
  components: {
    LoanAmountSection,
    SelectableProductList,
    ApplyFooter
  },
  data() {
    return {
      loanAmount: '48,000',
      selectedProducts: [], // 添加选中产品状态
      productList: [
        {
          logo: 'https://cdn.oss-unos.hmctec.cn/common/path/product-logo-1.png',
          name: '极速贷',
          tag: '正规品牌',
          rate: '7.2-9%',
          amount: '200,000',
          installment: '6-36',
          applicants: '2,386'
        },
        {
          logo: 'https://cdn.oss-unos.hmctec.cn/common/path/product-logo-2.png',
          name: '闪电贷',
          tag: '极速放款',
          rate: '8.8-12%',
          amount: '150,000',
          installment: '3-24',
          applicants: '1,865'
        },
        {
          logo: 'https://cdn.oss-unos.hmctec.cn/common/path/product-logo-3.png',
          name: '信用贷',
          tag: '纯信用',
          rate: '9.5-14%',
          amount: '100,000',
          installment: '12-48',
          applicants: '3,745'
        }
      ]
    }
  },
  methods: {
    updateLoanAmount(value) {
      this.loanAmount = value
    },
    onInputFocus() {
      console.log('输入框获得焦点')
    },
    onInputBlur() {
      console.log('输入框失去焦点')
    },
    setAmount(amount) {
      this.loanAmount = amount.toLocaleString()
    },
    // 处理列表选择变化
    handleSelectionChange(products) {
      this.selectedProducts = products
    },
    // 处理申请按钮点击
    onProductsApply() {
      // 使用 data 中的 selectedProducts
      if (this.selectedProducts.length === 0) {
        uni.showToast({ title: '请先选择产品', icon: 'none' })
        return
      }
      console.log('申请产品:', this.selectedProducts)
      const productNames = this.selectedProducts.map((p) => p.name).join('、')
      // TODO: 处理产品申请逻辑
      uni.showToast({
        title: `申请${productNames}成功`,
        icon: 'success'
      })
    },
    // 处理打开隐私政策
    openPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/rich/privacy-policy'
      })
    },
    // 处理打开用户协议
    openUserAgreement() {
      uni.navigateTo({
        url: '/pages/rich/user-agreement'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.apply-page {
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/276067e5eed343f49b3cfc46dbd70aa8.png);
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  padding-top: 100rpx;
  padding-bottom: 88rpx;
}
</style>
