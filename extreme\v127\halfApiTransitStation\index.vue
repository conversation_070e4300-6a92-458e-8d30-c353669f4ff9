<template>
  <view class="page-container">
    <half-api-transit-station @progress-complete="handleProgressComplete" />
  </view>
</template>

<script>
import HalfApiTransitStation from '@/components/dai-yue-tong/halfApiTransitStation/index.vue'

export default {
  name: 'HalfApiTransitStationPage',
  components: {
    HalfApiTransitStation
  },
  methods: {
    handleProgressComplete() {
      // 可以在这里处理进度完成后的操作
      console.log('进度完成')
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
}
</style>
