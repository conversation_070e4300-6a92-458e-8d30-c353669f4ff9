import { encryptByDES, decryptByDES } from '@/utils/encrypt.js'
import store from '@/store'

let baseUrl = ''

// true：发布模式
// false：本地开发模式
const isBuild = process.env.NODE_ENV === 'production'

const urlMap = {
  // 正式服
  prod: 'https://api.whxmxr.cn/h5/api',
  // 贷悦通正式服
  dytProd: 'https://dytapi.helianshengkj.cn/h5/api',
  // 测试服
  test: 'http://test.k8.whxmxr.cn/api',
  // 局域网
  zly: 'http://192.168.0.44:9076/api',
  lgq: 'http://192.168.0.4:9076/api',
  yqy: 'http://192.168.0.20:9076/api',
  lh: 'http://192.168.1.5:9076/api',
  mock: 'http://127.0.0.1:4523/m1/950149-329418-default'
}

if (isBuild) {
  baseUrl = urlMap.prod
} else {
  baseUrl = urlMap.test
}

const request = ({ data = {}, method, url, isEncrypt, deEncrypt, header = {} }) => {
  return new Promise((resolve, reject) => {
    data = {
      ...data,
      deviceType: uni.getSystemInfoSync().platform
    }

    if (isEncrypt) {
      data = {
        encrypt: encryptByDES(JSON.stringify({ ...data }))
      }
    }

    const defaultHeader = {
      'content-type': 'application/json'
    }
    const finalHeader = { ...defaultHeader, ...header }

    uni.request({
      url: `${baseUrl}${url}`,
      method: method || 'GET',
      data,
      timeout: 30000,
      header: finalHeader,
      success: (res) => {
        if (res.statusCode != 200) {
          return errMsg({
            code: res.statusCode,
            msg: `响应错误: ${JSON.stringify(res)}`
          })
        }
        if (deEncrypt) {
          res.data.data = decryptByDES(res.data.data)
        }
        resolve(res.data)
      },
      fail: (res) => {
        if (res.errMsg.includes('statusCode:-1')) {
          errMsg({
            code: 4002,
            msg: '哎呀，网络不给力呀！'
          })
        } else if (res.errMsg == 'request:fail') {
          errMsg({
            code: 4002,
            msg: '网络连接失败'
          })
        } else if (res.errMsg == 'request:fail timeout') {
          errMsg({
            code: 4002,
            msg: '网络请求超时'
          })
        } else {
          errMsg({
            code: 4002,
            msg: `未知错误-request: ${JSON.stringify(res)}`
          })
        }
      },
      complete: () => {
        uni.stopPullDownRefresh()
      }
    })
  }).catch((err) => {
    console.log(err, 'errr')
    errMsg({
      code: 4001,
      msg: `未知错误-catch-${JSON.stringify(err)}`
    })
  })
}

function errMsg(err) {}

/**
 * POST请求
 * @param {Object} options 请求配置
 * @param {Object} options.data 请求参数
 * @param {String} options.url 接口地址
 * @param {Boolean} [options.isEncrypt=true] 参数是否需要加密
 * @param {Boolean} [options.deEncrypt=false] 响应数据是否需要解密
 * @param {Object} [options.header={}] 自定义请求头
 * @returns {Promise} 请求Promise对象
 */
export function $post({ data, url, isEncrypt = true, deEncrypt = false, header = {} }) {
  return request({
    data,
    method: 'POST',
    url,
    isEncrypt,
    deEncrypt,
    header
  })
}

/**
 * GET请求
 * @param {Object} options 请求配置
 * @param {Object} options.data 请求参数
 * @param {String} options.url 接口地址
 * @param {Boolean} [options.isEncrypt=true] 参数是否需要加密
 * @param {Boolean} [options.deEncrypt=false] 响应数据是否需要解密
 * @param {Object} [options.header={}] 自定义请求头
 * @returns {Promise} 请求Promise对象
 */
export function $get({ data, url, isEncrypt = true, deEncrypt = false, header = {} }) {
  return request({
    data,
    method: 'GET',
    url,
    isEncrypt,
    deEncrypt,
    header
  })
}

export function getBaseUrl() {
  return baseUrl
}
