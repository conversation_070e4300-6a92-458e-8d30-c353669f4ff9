<template>
  <view class="page-container">
    <header-section />
    <main-product @apply="handleMainProductApply" />
    <notification-bar :message="notificationMessage" />
    <product-list :productList="products" @apply="handleProductApply" />
  </view>
</template>

<script>
import HeaderSection from '@/components/dai-yue-tong/applyOther/HeaderSection.vue'
import MainProduct from '@/components/dai-yue-tong/applyOther/MainProduct.vue'
import NotificationBar from '@/components/dai-yue-tong/applyOther/NotificationBar.vue'
import ProductList from '@/components/dai-yue-tong/applyOther/ProductList.vue'

export default {
  components: {
    HeaderSection,
    MainProduct,
    NotificationBar,
    ProductList
  },
  data() {
    return {
      notificationMessage: '183****4565已申请四款产品，成功获得额度',
      products: [
        {
          logo: '',
          name: '提钱花',
          tag: '正规品牌',
          rate: '20-30%',
          amount: '2,000,000',
          installment: '6-48',
          applicants: '326'
        }
        // 可以添加更多产品
      ]
    }
  },
  methods: {
    handleMainProductApply() {
      // 处理主推产品申请
      console.log('申请主推产品')
    },
    handleProductApply(product) {
      // 处理产品列表中的产品申请
      console.log('申请产品', product)
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  padding-top: 80rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, rgba(184, 254, 255, 0.43) 0%, #f3f8f9 36%, #f3f8f9 100%);
}
</style>
