<template>
  <div>
    <div id="captcha-button">点击弹出验证码A</div>
    <div id="captcha-element"></div>
    <button id="refresh-button" @click="initCaptcha">重新初始化验证码</button>
  </div>
</template>

<script>
  export default {
    name: 'CaptchaA',
    props: {},
    data() {
      return {
        captcha: null,
        captchaButton: null,
      }
    },
    mounted() {
      this.captchaButton = document.getElementById('captcha-button');
      this.initCaptcha();
    },
    methods: {
      getInstance(instance) {
        this.captcha = instance
      },
      success(captchaVerifyParam) {
        // 入参为验签captchaVerifyParam
        // 1.向后端发起业务请求进行验证码验签captchaVerifyParam校验
        // 2.根据校验结果来进行业务处理
        // 3.如业务需要重新进行验证码验证，调用验证码初始化方法initAliyunCaptcha重新初始化验证码
        console.log(captchaVerifyParam);
      },
      // 验证码验证不通过回调函数
      fail(error) {
        console.error(error);
      },
      initCaptcha() {
        window.initAliyunCaptcha({
          SceneId: 'xxxxxxx', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
          mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
          element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
          button: '#captcha-button', // 触发验证码弹窗的元素。
          success: this.success,
          fail: this.fail,
          getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
          slideStyle: {
            width: 360,
            height: 40,
          }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
          language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
        })
      }
    },
  }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  #captcha-button {
    z-index: 99;
    width: 150px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid transparent;
    cursor: pointer;
    background-color: hsla(160, 100%, 37%, 1);
    color: #fff;
    padding: 8px 0;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
  }
</style>
