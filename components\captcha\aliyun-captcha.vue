<template>
  <view class="captcha-container">
    <!-- 触发验证码的按钮 -->
    <view 
      :id="buttonId" 
      class="captcha-button" 
      @click="handleButtonClick"
    >
      <slot>
        <view class="default-button">身份验证</view>
      </slot>
    </view>
    
    <!-- 验证码渲染容器 -->
    <view :id="elementId" class="captcha-element"></view>
  </view>
</template>

<script>
export default {
  name: 'AliyunCaptcha',
  props: {
    // 场景ID，需要从阿里云控制台获取
    sceneId: {
      type: String,
      default: 'azlh3tis' // 需要配置实际的场景ID
    },
    // 验证码模式
    mode: {
      type: String,
      default: 'popup'
    },
    // 滑块样式配置
    slideStyle: {
      type: Object,
      default: () => ({
        width: 360,
        height: 40
      })
    },
    // 语言类型
    language: {
      type: String,
      default: 'cn'
    },
    // 是否自动初始化
    autoInit: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      captcha: null,
      captchaButton: null,
      buttonId: `captcha-button-${this._uid}`,
      elementId: `captcha-element-${this._uid}`,
      isInitialized: false
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      if (this.autoInit) {
        this.initCaptcha()
      }
    })
  },
  
  beforeDestroy() {
    // 清理验证码实例
    if (this.captcha) {
      try {
        this.captcha.destroy && this.captcha.destroy()
      } catch (error) {
        console.warn('清理验证码实例时出错:', error)
      }
    }
  },
  
  methods: {
    // 获取验证码实例
    getInstance(instance) {
      this.captcha = instance
      this.$emit('getInstance', instance)
    },
    
    // 验证成功回调
    success(captchaVerifyParam) {
      console.log('人机验证成功:', captchaVerifyParam)
      this.$emit('success', captchaVerifyParam)
    },
    
    // 验证失败回调
    fail(error) {
      console.error('人机验证失败:', error)
      this.$emit('fail', error)
    },
    
    // 初始化验证码
    initCaptcha() {
      if (!window.initAliyunCaptcha) {
        console.error('阿里云验证码SDK未加载，请检查index.html中的SDK引入')
        this.$emit('error', '验证码SDK未加载')
        return
      }
      
      try {
        window.initAliyunCaptcha({
          SceneId: this.sceneId,
          mode: this.mode,
          element: `#${this.elementId}`,
          button: `#${this.buttonId}`,
          success: this.success,
          fail: this.fail,
          getInstance: this.getInstance,
          slideStyle: this.slideStyle,
          language: this.language
        })
        
        this.isInitialized = true
        this.$emit('initialized')
      } catch (error) {
        console.error('初始化验证码失败:', error)
        this.$emit('error', error)
      }
    },
    
    // 重新初始化验证码
    reinitCaptcha() {
      this.isInitialized = false
      this.$nextTick(() => {
        this.initCaptcha()
      })
    },
    
    // 手动触发验证
    trigger() {
      if (this.captcha && this.captcha.verify) {
        this.captcha.verify()
      } else if (!this.isInitialized) {
        this.initCaptcha()
      }
    },
    
    // 处理按钮点击
    handleButtonClick() {
      this.$emit('buttonClick')
      // 如果未初始化，先初始化
      if (!this.isInitialized) {
        this.initCaptcha()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.captcha-container {
  .captcha-button {
    cursor: pointer;
    
    .default-button {
      padding: 22rpx;
      background: linear-gradient(270deg, #34b0fd 0%, #277bff 100%);
      border-radius: 546rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 58rpx;
    }
  }
  
  .captcha-element {
    // 验证码渲染容器样式
    // 通常由阿里云SDK自动处理样式
  }
}
</style>
