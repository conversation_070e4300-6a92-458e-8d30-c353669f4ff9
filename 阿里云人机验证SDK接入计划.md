# 阿里云人机验证SDK接入计划（Web端短信验证）

## 项目背景

当前uniapp vue2项目是一个多端金融H5应用，支持微信小程序、H5、微信公众号等多端部署。项目中已有完善的短信验证码发送机制，现需要在web端发送短信验证码前增加阿里云人机验证功能。

## 需求分析

- **目标**：在web端使用阿里云人机验证替换现有的短信验证码
- **范围**：仅处理H5/web端，其他平台不考虑
- **场景**：用户在需要验证的场景下，使用人机验证代替短信验证码进行身份验证

## 技术方案

### 1. SDK引入和基础配置

#### 1.1 修改index.html
在项目根目录的`index.html`文件中引入阿里云验证码SDK：

```html
<!-- 在<head>标签中添加 -->
<script>
  window.AliyunCaptchaConfig = {
    region: 'cn',
    prefix: 'your-prefix', // 需要配置实际的前缀
  };
</script>
<script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
```

### 2. 组件开发

#### 2.1 直接使用demo中的验证码组件
基于vue2-captcha-demo中的组件（CaptchaA和CaptchaB完全相同），创建适用于当前项目的验证码组件。

位置：`components/captcha/aliyun-captcha.vue`

功能：
- 直接复用demo中的验证码逻辑
- 适配uni-app环境
- 在验证成功后直接完成身份验证
- 完全替代短信验证码功能

#### 2.2 替换现有短信验证码流程
将原有的短信验证码流程替换为人机验证：

```javascript
// 原来的流程：输入手机号 → 获取短信验证码 → 输入验证码 → 验证通过
// 新的流程：输入手机号 → 点击按钮 → 人机验证 → 验证通过

async verifyIdentity() {
  // 直接进行人机验证
  this.showCaptcha = true // 显示验证码
}

// 验证码验证成功后的回调
onCaptchaSuccess(captchaVerifyParam) {
  // 验证通过，直接进行下一步操作（如登录、注册等）
  this.submitForm(captchaVerifyParam)
}
```

### 3. 业务页面集成

#### 3.1 页面改造方式

**替换短信验证码流程**，将原有的短信验证码输入框和获取验证码按钮替换为人机验证：

```vue
<template>
  <!-- 原来的短信验证码UI -->
  <!--
  <input v-model="form.code" placeholder="请输入验证码" />
  <button @click="clickGetCode" :disabled="codeCountdown > 0">
    {{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
  </button>
  -->

  <!-- 替换为人机验证按钮 -->
  <button @click="startVerification">
    身份验证
  </button>

  <!-- 人机验证组件 -->
  <aliyun-captcha
    v-if="showCaptcha"
    @success="onCaptchaSuccess"
    @fail="onCaptchaFail"
  />
</template>

<script>
export default {
  data() {
    return {
      showCaptcha: false,
      isVerified: false, // 标记是否已通过验证
      // 移除原有的验证码相关数据
      // form: { code: '' }, // 不再需要
      // codeCountdown: 0,   // 不再需要
    }
  },
  methods: {
    // 开始身份验证
    startVerification() {
      this.showCaptcha = true
    },

    // 验证码验证成功回调
    onCaptchaSuccess(captchaVerifyParam) {
      this.showCaptcha = false
      this.isVerified = true

      // 验证通过后，直接进行业务操作（如登录、注册等）
      this.submitForm(captchaVerifyParam)
    },

    // 验证码验证失败回调
    onCaptchaFail(error) {
      this.showCaptcha = false
      uni.showToast({
        title: '验证失败，请重试',
        icon: 'none'
      })
    },

    // 提交表单（原来需要验证码的地方）
    async submitForm(captchaVerifyParam) {
      // 先调用行为验证接口进行后端校验
      const verifyRes = await this.behaviorVerify({
        phone: this.form.phone,
        template: this.form.template,
        channelId: this.form.channelId,
        captchaVerifyParam
      })

      if (!verifyRes.success) {
        uni.showToast({
          title: verifyRes.msg || '验证失败',
          icon: 'none'
        })
        return
      }

      // 验证通过后，进行业务操作（如登录、注册等）
      const res = await saveLoan({
        phone: this.form.phone,
        channelId: this.form.channelId,
        captchaVerifyParam // 使用人机验证参数
      })
      // 处理结果...
    },

    // 行为验证接口调用
    async behaviorVerify(data) {
      return await $post({
        url: '/api/user/behaviorVerify',
        data
      })
    }
  }
}
</script>
```

### 4. 实施步骤

#### 第一阶段：基础设施搭建（1天）
1. ✅ 修改index.html引入SDK
2. ✅ 复制demo中的验证码组件到项目中
3. ✅ 适配uni-app环境

#### 第二阶段：核心功能开发（1-2天）
1. ✅ 在1-2个版本的页面中集成验证码
2. ✅ 修改getCodeHandler方法增加验证步骤
3. ✅ 测试验证码+短信发送的完整流程

#### 第三阶段：全面部署（2-3天）
1. ✅ 在所有需要的页面中集成验证码组件
2. ✅ 功能测试和兼容性验证
3. ✅ 用户体验优化

### 5. 技术实现要点

#### 5.1 用户交互流程
1. 用户输入手机号
2. 点击"身份验证"按钮
3. 弹出阿里云人机验证界面
4. 用户完成滑块或点选验证
5. 验证通过后直接完成身份验证，进行下一步操作
6. 无需短信验证码输入步骤

#### 5.2 组件复用
- 直接复用vue2-captcha-demo中的验证码组件代码
- CaptchaA和CaptchaB完全相同，选择其中一个即可
- 只需要适配uni-app的语法差异（div改为view等）

#### 5.3 替换原则
- 完全替换短信验证码流程
- 移除验证码输入框和倒计时逻辑
- 移除短信发送相关代码
- 使用人机验证参数进行后续业务操作

### 6. 后端接口集成

#### 6.1 行为验证接口
**接口地址**: `api/user/behaviorVerify`
**请求方法**: POST
**Content-Type**: application/json

**请求参数**:
```json
{
  "phone": "string",              // 手机号
  "template": "string",           // 模板号
  "channelId": "integer",         // 渠道id
  "captchaVerifyParam": "string"  // 校验参数（阿里云SDK返回）
}
```

**响应格式**:
```json
{
  "success": "boolean",
  "code": "integer",
  "msg": "string",
  "data": "boolean"
}
```

#### 6.2 接口调用流程
1. 用户完成阿里云人机验证后，SDK返回 `captchaVerifyParam`
2. 前端调用 `behaviorVerify` 接口进行后端校验
3. 后端验证通过后，用户可进行下一步操作（登录/注册等）
4. 完全替代原有的短信验证码流程

#### 6.3 接口集成示例
```javascript
// 验证码验证成功回调
async onCaptchaSuccess(captchaVerifyParam) {
  this.showCaptcha = false

  // 调用后端验证接口
  const res = await this.behaviorVerify({
    phone: this.form.phone,
    template: this.form.template,
    channelId: this.form.channelId,
    captchaVerifyParam
  })

  if (res.success) {
    // 验证通过，进行下一步业务操作
    this.proceedToNextStep()
  } else {
    uni.showToast({
      title: res.msg || '验证失败',
      icon: 'none'
    })
  }
}

// 行为验证接口调用
async behaviorVerify(data) {
  return await $post({
    url: '/api/user/behaviorVerify',
    data
  })
}
```

### 7. 文件清单

#### 新建文件
- `components/captcha/aliyun-captcha.vue` - 验证码组件（复制自demo）

#### 修改文件
- `index.html` - 引入SDK
- `apis/user.js` - 添加 behaviorVerify 接口定义
- 具体需要改造的页面 - 根据后续需求确定

### 8. 预期效果

实施完成后，用户在web端的身份验证体验：
1. 输入手机号
2. 点击"身份验证"按钮
3. 弹出人机验证界面（滑块或点选）
4. 验证通过后自动调用 `behaviorVerify` 接口进行后端校验
5. 校验成功后直接完成身份验证，进入下一步
6. 无需等待短信或输入验证码

**核心优势**：
- 用户体验更流畅，无需等待短信
- 避免短信费用和到达率问题
- 防止恶意刷短信攻击和机器人攻击
- 直接复用成熟的demo代码
- 提高验证成功率和安全性
- 后端双重校验确保验证可靠性

### 9. 安全机制

#### 9.1 双重验证机制
- **前端验证**: 阿里云SDK进行人机识别验证
- **后端验证**: `behaviorVerify` 接口对验证参数进行二次校验
- **参数加密**: `captchaVerifyParam` 为阿里云加密后的验证参数

#### 9.2 防护能力
- 防止机器人自动化攻击
- 防止恶意刷短信行为
- 确保验证结果的真实性和完整性
- 提供完整的验证审计链路
