<template>
  <view class="page-container">
    <view class="auth-wrapper">
      <step-bar :current-step="currentStep" />
      <view class="auth-content">
        <view class="divider-block"></view>
        <personal-info-form
          v-if="currentStep === 0"
          ref="personalInfoForm"
          v-model="form.personal"
        />
        <asset-info-form v-if="currentStep === 1" ref="assetInfoForm" v-model="form.asset" />
        <credit-info-form v-if="currentStep === 2" ref="creditInfoForm" v-model="form.credit" />
        <step-buttons
          @next="handleNextStep"
          @prev="handlePrevStep"
          :show-prev="currentStep > 0"
          :show-next="currentStep < 2"
        />
        <privacy-notice />

        <Declaration />
      </view>
    </view>

    <footer-actions
      v-if="currentStep === 2"
      :is-agreed.sync="isAgreedToTerms"
      :agreement-list="agreementList"
      @submit="handleSubmit"
      @open-agreement="handleOpenAgreement"
    />

    <!-- 协议弹窗 -->
    <agreement-popup ref="agreementPopup" :agreementList="agreementList" @agree="handleAgree" />

    <!-- 加载弹窗 -->
    <loading-popup ref="loadingPopup" />
  </view>
</template>

<script>
import LoadingPopup from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import StepBar from '@/components/dai-yue-tong/auth/StepBar.vue'
import PersonalInfoForm from '@/components/dai-yue-tong/auth/PersonalInfoForm.vue'
import StepButtons from '@/components/dai-yue-tong/auth/StepButtons.vue'
import PrivacyNotice from '@/components/dai-yue-tong/auth/PrivacyNotice.vue'
import AssetInfoForm from '@/components/dai-yue-tong/auth/AssetInfoForm.vue'
import CreditInfoForm from '@/components/dai-yue-tong/auth/CreditInfoForm.vue'
import FooterActions from '@/components/dai-yue-tong/auth/FooterActions.vue'
import AgreementPopup from '@/components/dai-yue-tong/index/AgreementPopup.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { fetchFlow, saveUserInfo } from '@/apis/common-2'
import { nextFlowNode, setFlowIndex, setFlowNodes } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  async onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (this.form.consumerId) {
      this.fetchProtocol()
      this.fetchFlow()
    } else {
      this.navigateToEnd()
    }
  },
  components: {
    StepBar,
    PersonalInfoForm,
    StepButtons,
    PrivacyNotice,
    AssetInfoForm,
    CreditInfoForm,
    FooterActions,
    AgreementPopup,
    LoadingPopup,
    Declaration
  },
  data() {
    return {
      currentStep: 0,
      isAgreedToTerms: false,
      form: {
        templateVersion: 'v128',
        personal: {
          name: '',
          age: '',
          sex: null,
          cityName: '',
          cityCode: ''
        },
        asset: {
          isProvident: null,
          isHouse: null,
          isVehicle: null,
          isSocial: null,
          isInsure: null,
          isBusiness: null
        },
        credit: {
          sesameScoreIndex: -1,
          sesameId: null,
          isOverdue: false
        }
      },
      agreementList: []
    }
  },
  methods: {
    async fetchProtocol() {
      this.agreementList = await getAgreements('dyt-hls-ag2')
    },

    async fetchFlow() {
      const res = await fetchFlow(this.form)
      setFlowNodes(res.data.length ? res.data : null)
    },

    async handleNextStep() {
      const currentForm = this.getCurrentFormRef()
      if (!currentForm) return

      if (!currentForm.validateForm()) return

      if (this.currentStep < 2) {
        this.currentStep++
      }
    },
    handlePrevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    getCurrentFormRef() {
      const formRefs = {
        0: 'personalInfoForm',
        1: 'assetInfoForm',
        2: 'creditInfoForm'
      }
      return this.$refs[formRefs[this.currentStep]]
    },
    async handleSubmit() {
      const creditForm = this.$refs.creditInfoForm
      if (!creditForm || !creditForm.validateForm()) {
        return
      }

      if (!this.isAgreedToTerms) {
        uni.showToast({
          title: '请先同意协议',
          icon: 'none'
        })
        return
      }

      // 使用节流函数包装提交处理
      throttle(() => {
        setFlowIndex(0)
        this.submitHandler()
      })
    },
    async submitHandler() {
      await saveUserInfo(this.getUserParams())
      setFlowIndex(0)
      this.navigateToNextFlow()
    },
    handleOpenAgreement() {
      if (this.agreementList.length > 0) {
        this.$refs.agreementPopup.open(0)
      } else {
        uni.showToast({
          title: '协议加载中，请稍后再试',
          icon: 'none'
        })
      }
    },
    async handleAgree() {
      this.isAgreedToTerms = true
      setFlowIndex(0)
      await saveUserInfo(this.getUserParams())
      this.navigateToNextFlow()
    },
    async navigateToNextFlow() {
      this.$refs.loadingPopup.open()
      let nextFlow // 将 nextFlow 移到 try 外部
      try {
        nextFlow = await nextFlowNode({
          // 赋值给外部变量
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        })
      } finally {
        this.$refs.loadingPopup.close()
      }

      const routeMap = {
        offline: '/extreme/v128/apply/index',
        wechat: '/extreme/v128/qw/index',
        overloan: '/extreme/v128/applyOther/index',
        end: '/extreme/v128/download/index',
        halfapi: '/extreme/v128/webviewDown/index',
        wechat_official_account: '/extreme/v128/wechatOfficialAccount/index'
      }

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = {
        ...this.form,
        ...this.getUserParams()
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },
    getUserParams() {
      const params = {}
      params.cityCode = this.form.personal.cityCode
      params.name = this.form.personal.name
      params.age = this.form.personal.age
      params.sex = this.form.personal.sex
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.sesameId = this.form.credit.sesameId
      params.isOverdue = this.form.credit.isOverdue
      params.isProvident = this.form.asset.isProvident
      params.isHouse = this.form.asset.isHouse
      params.isVehicle = this.form.asset.isVehicle
      params.isSocial = this.form.asset.isSocial
      params.isInsure = this.form.asset.isInsure
      params.isBusiness = this.form.asset.isBusiness

      return params
    },
    navigateToEnd() {
      const path = '/extreme/v128/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding-top: 222rpx;
  height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/1a661e8ffc944f2f97b7f775f0491628.png);
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  overflow: hidden;

  .auth-wrapper {
    width: 750rpx;
    background: #ffffff;
    border-radius: 60rpx 60rpx 0 0;
    height: calc(100vh - 222rpx);

    .auth-content {
      height: calc(100vh - 222rpx - 150rpx);
      overflow-y: auto;
      padding-bottom: calc(666rpx + env(safe-area-inset-bottom));
    }
  }
}

.divider-block {
  width: 750rpx;
  height: 8rpx;
  background: #f2f2f2;
}
</style>
