<template>
  <view class="page-container">
    <view class="auth-wrapper">
      <step-bar :current-step="currentStep" />
      <view class="divider-block"></view>
      <personal-info-form
        v-if="currentStep === 0"
        ref="personalInfoForm"
        v-model="formData.personal"
      />
      <asset-info-form v-if="currentStep === 1" ref="assetInfoForm" v-model="formData.asset" />
      <credit-info-form v-if="currentStep === 2" ref="creditInfoForm" v-model="formData.credit" />
      <step-buttons
        @next="handleNextStep"
        @prev="handlePrevStep"
        :show-prev="currentStep > 0"
        :show-next="currentStep < 2"
      />
      <privacy-notice />
    </view>

    <footer-actions
      v-if="currentStep === 2"
      @submit="handleSubmit"
      @open-agreement="handleOpenAgreement"
    />
  </view>
</template>

<script>
import StepBar from '@/components/dai-yue-tong/auth/StepBar.vue'
import PersonalInfoForm from '@/components/dai-yue-tong/auth/PersonalInfoForm.vue'
import StepButtons from '@/components/dai-yue-tong/auth/StepButtons.vue'
import PrivacyNotice from '@/components/dai-yue-tong/auth/PrivacyNotice.vue'
import AssetInfoForm from '@/components/dai-yue-tong/auth/AssetInfoForm.vue'
import CreditInfoForm from '@/components/dai-yue-tong/auth/CreditInfoForm.vue'
import FooterActions from '@/components/dai-yue-tong/auth/FooterActions.vue'

export default {
  components: {
    StepBar,
    PersonalInfoForm,
    StepButtons,
    PrivacyNotice,
    AssetInfoForm,
    CreditInfoForm,
    FooterActions
  },
  data() {
    return {
      currentStep: 0,
      formData: {
        personal: {
          name: '',
          age: '',
          sex: null,
          cityName: '',
          cityCode: ''
        },
        asset: {
          isProvident: true,
          isHouse: true,
          isVehicle: true,
          isSocial: true,
          isInsure: true,
          isBusiness: true
        },
        credit: {
          sesameScoreIndex: 0,
          sesameId: 115,
          isOverdue: false
        }
      }
    }
  },
  methods: {
    async handleNextStep() {
      // 获取当前表单的引用
      const currentForm = this.getCurrentFormRef()
      if (!currentForm) return

      // 验证当前表单
      if (!currentForm.validateForm()) return

      // 切换到下一步
      if (this.currentStep < 2) {
        this.currentStep++
      }
    },
    handlePrevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    getCurrentFormRef() {
      const formRefs = {
        0: 'personalInfoForm',
        1: 'assetInfoForm',
        2: 'creditInfoForm'
      }
      return this.$refs[formRefs[this.currentStep]]
    },
    handleSubmit() {
      // 确保当前是最后一步且验证通过
      if (this.currentStep === 2) {
        const creditForm = this.$refs.creditInfoForm
        if (creditForm && creditForm.validateForm()) {
          // 组装提交数据
          const submitData = {
            // 个人信息
            name: this.formData.personal.name,
            age: this.formData.personal.age,
            sex: this.formData.personal.sex,
            cityName: this.formData.personal.cityName,
            cityCode: this.formData.personal.cityCode,

            // 资产信息
            hasProvidentFund: this.formData.asset.isProvident,
            hasHouse: this.formData.asset.isHouse,
            hasVehicle: this.formData.asset.isVehicle,
            hasSocialSecurity: this.formData.asset.isSocial,
            hasInsurance: this.formData.asset.isInsure,
            hasBusinessLicense: this.formData.asset.isBusiness,

            // 信用信息
            sesameScore: this.formData.credit.sesameId,
            hasOverdue: this.formData.credit.isOverdue
          }

          console.log('提交的表单数据：', {
            原始数据: this.formData,
            提交数据: submitData
          })
          // TODO: 处理提交逻辑
        }
      }
    },
    handleOpenAgreement() {
      // TODO: 处理打开协议逻辑
      console.log('打开协议')
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding-top: 222rpx;
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/1a661e8ffc944f2f97b7f775f0491628.png);
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;

  .auth-wrapper {
    width: 750rpx;
    background: #ffffff;
    border-radius: 60rpx 60rpx 0 0;
    min-height: calc(100vh - 222rpx);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

.divider-block {
  width: 750rpx;
  height: 8rpx;
  background: #f2f2f2;
}
</style>
