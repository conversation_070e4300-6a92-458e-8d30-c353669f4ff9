<template>
  <view class="page-container">
    <header-section />
    <main-product :product="mainProduct" @apply="handleMainProductApply" />
    <notification-bar :message="notificationMessage" />
    <product-list :productList="productList" @apply="handleProductApply" />
    <Loading ref="loading" />

    <Declaration />
  </view>
</template>

<script>
import HeaderSection from '@/components/dai-yue-tong/applyOther/HeaderSection.vue'
import MainProduct from '@/components/dai-yue-tong/applyOther/MainProduct.vue'
import NotificationBar from '@/components/dai-yue-tong/applyOther/NotificationBar.vue'
import ProductList from '@/components/dai-yue-tong/applyOther/ProductList.vue'
import Loading from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'
import { getDcAutoFlag } from '@/apis/common'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'

export default {
  components: {
    HeaderSection,
    MainProduct,
    NotificationBar,
    ProductList,
    Loading,
    Declaration
  },
  data() {
    return {
      form: {},
      notificationMessage: '183****4565已申请四款产品，成功获得额度',
      productList: [],
      mainProduct: {}
    }
  },
  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.getFlowData()
    if (this.mainProduct.platformId && this.mainProduct.id) {
      this.getDcAutoFlag()
    }
  },
  methods: {
    async getDcAutoFlag() {
      const res = await getDcAutoFlag({
        platformId: this.mainProduct.platformId,
        productId: this.mainProduct.id
      })
      // data = 1 不自动 ，data = 2 自动
      if (res.data == 2) {
        this.applyProduct(this.mainProduct)
      }
    },
    async getFlowData() {
      const flowData = getFlowData('overloan')

      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
      }
    },
    handleMainProductApply() {
      throttle(() => {
        this.applyProduct(this.mainProduct)
      })
    },
    handleProductApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },
    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        // 设置 url 到 vuex
        this.$u.vuex('vuex_overloanWebview.url', res.data)
        // 跳转到 overloan-webview 页面
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v128/overloanWebview/index?param=${urlParamString}`
        })
        return
      }

      this.navigateToNextFlow()
    },
    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v128/apply/index',
        wechat: '/extreme/v128/qw/index',
        overloan: '/extreme/v128/applyOther/index',
        end: '/extreme/v128/download/index',
        halfapi: '/extreme/v128/webviewDown/index',
        wechat_official_account: '/extreme/v128/wechatOfficialAccount/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      this.$refs.loading.close()

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  padding-top: 80rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, rgba(184, 254, 255, 0.43) 0%, #f3f8f9 36%, #f3f8f9 100%);
}
</style>
