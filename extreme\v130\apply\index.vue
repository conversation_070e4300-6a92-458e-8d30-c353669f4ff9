<template>
  <view class="apply-page">
    <!-- 贷款金额选择区域 -->
    <loan-amount-section
      :loan-amount="loanAmount"
      @update:loan-amount="updateLoanAmount"
      @input-focus="onInputFocus"
      @input-blur="onInputBlur"
      @set-amount="setAmount"
    />

    <!-- 产品列表 -->
    <selectable-product-list
      :product-list="productList"
      :default-selected-all="true"
      @selection-change="handleSelectionChange"
    />

    <!-- 底部申请区域 -->
    <apply-footer
      :selected-count="selectedProducts.length"
      :is-agreed="isAgreedToTerms"
      :agreement-list="agreementList"
      @apply="onProductsApply"
      @open-agreement="openAgreement"
      @update:is-agreed="(value) => (isAgreedToTerms = value)"
    />

    <!-- 协议弹窗 -->
    <agreement-popup ref="agreementPopup" :agreementList="agreementList" @agree="handleAgree" />

    <Loading ref="loading" />

    <Declaration />
  </view>
</template>

<script>
import LoanAmountSection from '@/components/dai-yue-tong/index/LoanAmountSection.vue'
import SelectableProductList from '@/components/dai-yue-tong/apply/SelectableProductList.vue'
import ApplyFooter from '@/components/dai-yue-tong/apply/ApplyFooter.vue'
import AgreementPopup from '@/components/dai-yue-tong/index/AgreementPopup.vue'
import Loading from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import { applyFormProduct } from '@/apis/common-2'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'
import { formatAmount, parseAmount } from '@/utils/amount.js'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'ApplyPage',
  components: {
    LoanAmountSection,
    SelectableProductList,
    ApplyFooter,
    AgreementPopup,
    Loading,
    Declaration
  },
  data() {
    return {
      loanAmount: '48,000', // 初始化为带逗号的格式
      rawAmount: '48000', // 存储无逗号的原始数值
      selectedProducts: [], // 添加选中产品状态
      isAgreedToTerms: false,
      agreementList: [],
      productList: [], // 用于显示的格式化产品列表
      originalProductList: [], // 存储原始产品数据
      form: {
        appUserId: '',
        channelId: '',
        consumerId: ''
      }
    }
  },
  onLoad({ param }) {
    if (param) {
      const decodedParam = decryptByDES(decodeURIComponent(param))
      Object.assign(this.form, decodedParam)
    }

    this.fetchProtocol()
    this.getFlowData()
  },
  methods: {
    updateLoanAmount(value) {
      this.loanAmount = value
    },
    onInputFocus() {
      this.loanAmount = this.rawAmount.toString()
    },
    onInputBlur() {
      // 先确保数值有效
      if (this.loanAmount) {
        // 移除非数字字符，保存原始数值
        this.rawAmount = parseAmount(this.loanAmount).toString()
        // 使用项目提供的格式化工具函数
        this.loanAmount = formatAmount(this.rawAmount)
      }
    },
    setAmount(amount) {
      this.rawAmount = amount.toString()
      this.loanAmount = formatAmount(this.rawAmount)
    },
    // 处理列表选择变化
    handleSelectionChange(products) {
      this.selectedProducts = products
      this.updateProductProtocols()
    },
    // 处理申请按钮点击
    onProductsApply() {
      if (!this.validateSubmit()) {
        return
      }
      this.submitProducts()
    },
    // 验证提交条件
    validateSubmit() {
      if (this.selectedProducts.length === 0) {
        uni.showToast({ title: '请先选择产品', icon: 'none' })
        return false
      }
      if (!this.isAgreedToTerms) {
        uni.showToast({ title: '请先同意协议', icon: 'none' })
        return false
      }
      return true
    },
    // 提交产品申请
    async submitProducts() {
      try {
        // 根据选中的产品的productId和platformType找到对应的原始产品数据
        const selectedOriginalProducts = this.selectedProducts
          .map((product) =>
            this.originalProductList.find(
              (p) => p.productId == product.productId && p.platformType == product.platformType
            )
          )
          .filter(Boolean)

        const params = {
          channelId: this.form.channelId,
          consumerId: this.form.appUserId,
          applyProduct: selectedOriginalProducts
        }

        await applyFormProduct(params)

        // 显示loading
        this.$refs.loading.open()

        // 申请成功后，获取下一个流程节点
        const nextFlow = await nextFlowNode({
          templateVersion: 'v130',
          channelId: this.form.channelId,
          consumerId: this.form.appUserId
        })

        // 关闭loading
        this.$refs.loading.close()

        // 根据返回的流程类型进行跳转
        const routeMap = {
          offline: '/extreme/v130/apply/index',
          wechat: '/extreme/v130/qw/index',
          overloan: '/extreme/v130/applyOther/index',
          end: '/extreme/v130/download/index',
          halfapi: '/extreme/v130/webviewDown/index',
          wechat_official_account: '/extreme/v130/wechatOfficialAccount/index'
        }

        if (nextFlow.flow === 'halfapi' && nextFlow.url) {
          this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
        }

        const path = routeMap[nextFlow.flow]
        if (path) {
          const urlParam = this.form
          const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
          uni.navigateTo({
            url: `${path}?param=${urlParamString}`
          })
        }
      } catch (error) {
        console.error('申请产品失败:', error)
        uni.showToast({
          title: error.message || '申请失败，请重试',
          icon: 'none'
        })
        // 确保loading被关闭
        this.$refs.loading.close()
      }
    },
    async fetchProtocol() {
      this.agreementList = await getAgreements('dyt-hls-ag1')
      // 初始化时同步更新产品协议
      this.updateProductProtocols()
    },
    async getFlowData() {
      const flowData = await getFlowData('offline')
      if (flowData) {
        // 保存原始产品数据
        this.originalProductList = flowData.productList || []

        // 转换产品数据格式以匹配组件期望的结构
        this.productList = flowData.productList.map((product) => ({
          productId: product.productId, // 保存productId用于匹配
          platformType: product.platformType, // 保存platformType用于匹配
          logo: product.logo,
          name: product.productName,
          tag: '正规品牌', // 默认标签
          rate: '7.2-9%', // 默认利率范围
          amount: product.loanableFundsBig, // 格式化金额
          installment: '6-36', // 默认分期期数
          applicants: product.putinPush || '2,386', // 使用放款人数，如果没有则使用默认值
          matchingDegree: Math.floor(Math.random() * 5 + 95),
          protocolList: product.protocolList || [] // 添加产品协议字段
        }))

        // 保存其他数据
        if (flowData.appUserId) {
          this.form.appUserId = flowData.appUserId
        } else {
          this.form.appUserId = this.form.consumerId
        }
        if (flowData.channelId) {
          this.form.channelId = flowData.channelId
        }
      }
    },
    // 处理打开协议
    openAgreement(index) {
      if (this.agreementList.length > 0) {
        this.$refs.agreementPopup.open(index)
      } else {
        uni.showToast({
          title: '协议加载中，请稍后再试',
          icon: 'none'
        })
      }
    },
    handleAgree() {
      this.isAgreedToTerms = true
      // 同意协议后自动提交
      if (this.validateSubmit()) {
        this.submitProducts()
      }
    },
    // 更新产品协议
    updateProductProtocols() {
      // 先保留原来从后端获取的协议
      const backendProtocols = this.agreementList.filter((item) => item.protocolId)

      // 从选中的产品中提取协议
      const productProtocols = []
      this.selectedProducts.forEach((product) => {
        const originalProduct = this.originalProductList.find(
          (p) => p.productId == product.productId && p.platformType == product.platformType
        )

        if (
          originalProduct &&
          originalProduct.protocolList &&
          originalProduct.protocolList.length > 0
        ) {
          originalProduct.protocolList.forEach((protocol) => {
            // 直接添加协议，不检查重复
            productProtocols.push(protocol)
          })
        }
      })

      // 将后端协议和产品协议合并
      this.agreementList = [...backendProtocols, ...productProtocols]
    }
  }
}
</script>

<style lang="scss" scoped>
.apply-page {
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/276067e5eed343f49b3cfc46dbd70aa8.png);
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  padding-top: 100rpx;
  padding-bottom: 666rpx;
}
</style>
