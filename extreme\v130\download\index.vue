<template>
  <view class="app-container">
    <dyt-success-section />
    <dyt-loan-info-section />
    <dyt-qw-info-section />

    <Declaration />
  </view>
</template>

<script>
import DytSuccessSection from '@/components/dai-yue-tong/download/SuccessSection.vue'
import DytLoanInfoSection from '@/components/dai-yue-tong/download/LoanInfoSection.vue'
import DytQwInfoSection from '@/components/dai-yue-tong/qw/InfoSection.vue'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'

export default {
  name: 'Download',
  components: {
    DytSuccessSection,
    DytLoanInfoSection,
    DytQwInfoSection,
    Declaration
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  // 页面容器样式
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/276067e5eed343f49b3cfc46dbd70aa8.png);
  min-height: 100vh;
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  padding-top: 200rpx;
  padding-bottom: 88rpx;
}
</style>
