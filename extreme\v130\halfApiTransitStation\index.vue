<template>
  <view class="page-container">
    <half-api-transit-station />
  </view>
</template>

<script>
import HalfApiTransitStation from '@/components/dai-yue-tong/halfApiTransitStation/index.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'

export default {
  name: 'HalfApiTransitStationPage',
  components: {
    HalfApiTransitStation
  },
  data() {
    return {
      form: {
        consumerId: '',
        firstFlag: '',
        templateVersion: 'v130'
      }
    }
  },

  onLoad({ param, consumerId, first }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (first) {
      this.form.firstFlag = first
    }

    if (consumerId) {
      this.form.consumerId = decryptByDES(decodeURIComponent(consumerId))
      this.$u.vuex('vuex_consumerId', this.form.consumerId)
      this.navigateToNextPage()
    } else {
      this.navigateToEnd()
    }
  },

  methods: {
    async navigateToNextPage() {
      try {
        const routeMap = {
          offline: '/extreme/v130/apply/index',
          wechat: '/extreme/v130/qw/index',
          overloan: '/extreme/v130/applyOther/index',
          end: '/extreme/v130/download/index',
          halfapi: '/extreme/v130/webviewDown/index',
          wechat_official_account: '/extreme/v130/wechatOfficialAccount/index'
        }

        const nextFlow = await nextFlowNode({
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        })

        if (nextFlow.flow === 'halfapi') {
          this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
        }

        const path = routeMap[nextFlow.flow]
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `${path}?param=${urlParamString}`
        })
      } catch (e) {
        this.navigateToEnd()
      }
    },
    navigateToEnd() {
      uni.navigateTo({
        url: `/extreme/v130/download/index?param=${encodeURIComponent(encryptByDES(JSON.stringify(this.form)))}&result=2`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
}
</style>
