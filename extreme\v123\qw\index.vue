<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <view class="page-content">
        <view class="page-title">
          <view class="title">您的额度可以去</view>
          <view class="title">
            <view class="icon-wechat-2"></view>
            <view class="title">微信使用啦</view>
          </view>
        </view>

        <view class="card">
          <view class="card-image card-image-1"></view>
        </view>

        <view class="card">
          <view class="card-image card-image-2"></view>
        </view>
      </view>
      <view class="page-footer">
        <!-- <view class="agreement" @click="clickAgreement">
          <view class="agreement-text">
            本人已知晓
            <text class="name" v-for="item in agreementList" :key="item.protocolId">
              《{{ item.name }}》
            </text>
            的所有内容，同意并授权平台推荐/匹配多个产品
          </view>
        </view> -->

        <template v-if="applyUrl">
          <a class="btn confirm-btn" :href="applyUrl" target="_blank">
            立即领取
            <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
          </a>
          <view class="btn cancel-btn" @click="clickCancel">取消</view>
        </template>

        <view class="confirm-tips">
          <view class="icon-wechat"></view>
          <text>使用您本人微信验证后才可领取额度</text>
        </view>
      </view>

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <view class="agreement-container">
            <view v-for="agreement in agreementList" :key="agreement.protocolId">
              <view class="agreement-content">
                <div v-html="agreement.content"></div>
              </view>
            </view>
          </view>
          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickConfirm">同意并继续</view>
          </view>
        </view>
      </uni-popup>

      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import {
  applyOnlineProduct,
  fetchWechatAutoJump,
  getProtocolDetail,
  getProtocolList
} from '@/apis/common-2'
import { formatRichText } from '@/utils/utils'
import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getApplyOnlineProductLink, matchingOnlineProduct } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'

export default {
  name: 'qwIndex',
  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      agreementList: [],
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      productList: [],
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: '',
      // 跳转链接
      applyUrl: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    // this.fetchProtocol()
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      this.applyUrl = getApplyOnlineProductLink({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        redirectUrl: encodeURIComponent(window.location.href)
      })

      if (isPageNested()) {
        await savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }

      await this.fetchWechatAutoJump()
      if (this.wechatAutoJump == 2) {
        // this.timerStart()
        this.confirmHandler()
      }
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
  },

  methods: {
    timerStart() {
      this.confirmCountdownNumber = 3
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }
      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.clickConfirm()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },

    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },

    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await matchingOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v123/qw/index',
        overloan: '/extreme/v123/applyOther/index',
        end: '/extreme/v123/download/index',
        wechat_official_account: '/extreme/v123/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },

    async fetchProtocol() {
      const agreementListRes = await getProtocolList({
        protocolSig: this.vuex_templateConfig.agreementKeyQw
      })
      this.agreementList = agreementListRes.data || []
      this.agreementList.forEach((agreement) => {
        getProtocolDetail({ protocolId: agreement.protocolId }).then((res) => {
          agreement.content = formatRichText(res.data.content, {})
        })
      })
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.timerStop()
      this.applyProduct()
    },

    clickCancel() {
      throttle(this.cancelHandler)
    },

    cancelHandler() {
      this.timerStop()
      this.navigateToNextFlow()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/theme/v123/export/qw.scss';
</style>
