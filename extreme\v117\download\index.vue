<script>
import Header from '@/components/header/header-hyh.vue'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: { Declaration, Header, Services }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <!-- <Header @click.native="navigateToIndex"/> -->

      <view class="feature-image">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/b7de3bc44d5847d3ac4345942e8939cd.png"
          class="icon-speaker"
        />
      </view>

      <Services />

      <Declaration />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  height: 100dvh;
  background: #f6f6f8;
  display: flex;
  flex-direction: column;

  .page-content {
    padding: 30rpx;
    flex: 1;
    overflow: hidden auto;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/de5eb8cf965c4ffb8411bf854a49c327.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }
}

.feature-image {
  position: relative;
  width: 100%;
  height: 634rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0c0ae4b70d20400292cf00f3adcccc49.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .icon-speaker {
    top: 150rpx;
    right: -50rpx;
    position: absolute;
    width: 250rpx;
    height: 250rpx;
  }
}
</style>
