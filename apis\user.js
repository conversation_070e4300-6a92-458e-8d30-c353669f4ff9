import { $get, $post } from '@/utils/request'

/**
 * 人机验证行为校验接口
 * @param {Object} data - 请求参数
 * @param {string} data.phone - 用户手机号
 * @param {string} data.template - 模板信息（可选）
 * @param {string} data.channelId - 渠道ID
 * @param {string} data.captchaVerifyParam - 阿里云人机验证返回的验证参数
 * @returns {Promise} 返回验证结果
 */
export const behaviorVerify = (data = {}) => {
  return $post({
    url: '/user/behaviorVerify',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}