<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <view class="page-title">
        <view class="title">您的额度可以去</view>
        <view class="title">
          <image
            class="icon-wechat-2"
            src="https://cdn.oss-unos.hmctec.cn/common/path/e6a104cfe31f4a08b2d80202da103a8f.png"
          />
          <view class="title">微信使用啦</view>
        </view>
      </view>

      <view class="card">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/6c4d792b0b4a48938c756330d6e68090.png"
        />
      </view>

      <view class="card">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/7f7cd289118f461094019efea19531ec.png"
        />
      </view>

      <Declaration />
    </view>
    <view class="page-footer">
      <view class="agreement" @click="clickAgreement">
        <view class="agreement-text">
          本人已知晓
          <text class="name" v-for="item in agreementList" :key="item.protocolId">
            《{{ item.name }}》
          </text>
          的所有内容，同意并授权平台推荐/匹配多个产品
        </view>
      </view>
      <view class="btn confirm-btn" @click="clickConfirm">
        立即领取
        <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
      </view>
      <view class="btn cancel-btn" @click="clickCancel">取消</view>
      <view class="confirm-tips">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/55328190f6c14d1297e166abb133b20a.png"
          class="icon-wechat"
        />
        <text>使用您本人微信验证后才可领取额度</text>
      </view>
    </view>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickConfirm">同意并继续</view>
        </view>
      </view>
    </uni-popup>

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import {
  applyOnlineProduct,
  fetchOnlineProduct,
  fetchWechatAutoJump,
  getProtocolDetail,
  getProtocolList
} from '@/apis/common-2'
import { formatRichText } from '@/utils/utils'
import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import Declaration from '@/components/footer/declaration-component/declaration-hr.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'

export default {
  name: 'qwIndex',
  components: {
    Declaration,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      agreementList: [],
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      productList: [],
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: ''
    }
  },

  onLoad({ param }) {
    uni.setNavigationBarTitle({
      title: '惠生活·融易贷-惠融钱包'
    })

    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.fetchProtocol()
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      await this.fetchWechatAutoJump()
      if (this.wechatAutoJump == 2) {
        this.timerStart()
      }
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
  },

  methods: {
    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },

    timerStart() {
      this.confirmCountdownNumber = 3
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }
      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.clickConfirm()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },

    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
        // limit: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v120/apply/index',
        wechat: '/extreme/v120/qw/index',
        overloan: '/extreme/v120/applyOther/index',
        end: '/extreme/v120/download/index',
        halfapi: '/extreme/v120/webviewDown/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },

    async fetchProtocol() {
      // 个人信息共享授权书，知情告知书
      const agreementListRes = await getProtocolList({
        protocolSig: 'hrqb-qw'
      })
      this.agreementList = agreementListRes.data || []
      this.agreementList.forEach((agreement) => {
        getProtocolDetail({ protocolId: agreement.protocolId }).then((res) => {
          agreement.content = formatRichText(res.data.content, {})
        })
      })
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.timerStop()
      this.applyProduct()
    },

    clickCancel() {
      throttle(this.cancelHandler)
    },

    cancelHandler() {
      this.timerStop()
      this.navigateToNextFlow()
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f6f6f8;
  // display: flex;
  // flex-direction: column;

  .page-content {
    height: 100vh;
    overflow: hidden auto;
    background: linear-gradient(180deg, #30c497 0%, #effbf9 48%);
    padding-bottom: 600rpx;
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      margin-bottom: 20rpx;
      padding: 0 30rpx;
      display: flex;
      gap: 5rpx;
      text-align: center;

      .agree-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: #2a6af7;
        }
      }
    }

    .btn {
      padding: 22rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      font-size: 40rpx;
      line-height: 56rpx;
      text-align: center;
    }

    .confirm-btn {
      margin-bottom: 15rpx;
      border: 1rpx solid #62d5ab;
      background: #62d5ab;
      color: #ffffff;
    }

    .cancel-btn {
      margin-bottom: 20rpx;
      border: 1rpx solid #999999;
      color: #999999;
    }

    .confirm-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 34rpx;

      .icon-wechat {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.page-title {
  padding-top: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    display: flex;
    align-items: center;
    color: #fff;
    line-height: 100rpx;
    font-size: 72rpx;
    font-weight: 400;
  }

  .icon-wechat-2 {
    margin-right: 20rpx;
    width: 89rpx;
    height: 73rpx;
  }
}

.card {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  image {
    width: 633rpx;
    height: 298rpx;
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0a9a3a75797c401ab0170f69c0b923e7.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: #2a6af7;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
