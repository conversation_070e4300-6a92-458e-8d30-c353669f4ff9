import Vue from 'vue'
import VueRouter from 'vue-router'
import <PERSON><PERSON><PERSON>_Page from '../views/CaptchaA_Page.vue'
import CaptchaB_Page from '../views/CaptchaB_Page.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/captchaA',
    name: 'Captcha<PERSON>',
    component: CaptchaA_Page,
    meta: {
      title: 'CaptchaA',
    },
  },
  {
    path: '/captchaB',
    name: 'CaptchaB',
    component: CaptchaB_Page,
    meta: {
      title: 'CaptchaB',
    },
  },
  // {
  //   path: "/about",
  //   name: "about",
  //   // route level code-splitting
  //   // this generates a separate chunk (about.[hash].js) for this route
  //   // which is lazy-loaded when the route is visited.
  //   component: () =>
  //     import(/* webpackChunkName: "about" */ "../views/AboutView.vue"),
  // },
]

const router = new VueRouter({
  routes,
})

export default router
