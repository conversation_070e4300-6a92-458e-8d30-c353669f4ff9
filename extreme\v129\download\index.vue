<script>
import Header from '@/components/header/header-jqgj.vue'
import Declaration from '@/components/footer/declaration-component/declaration-ycl.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: { Declaration, Header, Services }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <Header @click.native="navigateToIndex" />

      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/3b95a0be6d3f45439db8b580992af57d.png"
        class="feature-image"
      ></image>

      <Services />

      <Declaration />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  height: 100dvh;
  background: #f6f6f8;
  display: flex;
  flex-direction: column;

  .page-content {
    padding: 30rpx;
    flex: 1;
    overflow: hidden auto;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/8f99413b228c4ac5a7bd093592d05f6f.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }
}

.feature-image {
  width: 100%;
  height: 634rpx;
}
</style>
