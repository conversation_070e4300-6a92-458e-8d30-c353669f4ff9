<template>
  <view class="container">
    <view class="cover" v-if="show">当前为第三方网页，请注意核实信息谨防上当受骗</view>
    <web-view :src="url" class="wb-view"></web-view>
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import { getHalfApiProgress } from '@/apis/common'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'webviewDown',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      show: true,
      url: '',
      form: {},
      timer: null
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (this.vuex_webviewDown.url) {
        this.url = this.vuex_webviewDown.url
      }

      this.startPolling()
    } else {
      this.navigateToEnd()
    }

    setTimeout(() => {
      this.show = false
    }, 3000)
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },

  methods: {
    startPolling() {
      this.checkProgress()
      this.timer = setInterval(() => {
        this.checkProgress()
      }, 2000)
    },

    async checkProgress() {
      const res = await getHalfApiProgress({
        consumerId: this.form.consumerId
      })
      // 0 待回调，1 已回调
      if (res.data == 1) {
        clearInterval(this.timer)
        this.timer = null
        this.navigateToNextFlow()
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    navigateToEnd() {
      const path = '/extreme/v117/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 14rpx 32rpx;
    left: 0;
    top: 0;
    background: #e8ebf7;
    font-size: 24rpx;
    color: #2737A8;
    text-align: center;
  }
}
</style>
