<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatAmount, parseAmount } from '@/utils/amount'
import { applyFormProduct } from '@/apis/common-2'
import { getAgreements } from '@/utils/agreement'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hr.vue'
import Declaration from '@/components/footer/declaration-component/declaration-hr.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'

export default {
  name: 'ApplyIndex',
  components: {
    Declaration,
    Header,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        monthIndex: 0,
        demandAmount: '50000'
      },
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      activeForm: '',
      productList: [],
      monthlyPay: '',
      agreementList: [],
      productPopupData: {}
    }
  },

  onLoad({ param }) {
    uni.setNavigationBarTitle({
      title: '惠生活·融易贷-惠融钱包'
    })

    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.updateAmountUI()
    this.getFlowData()
    this.fetchProtocol()
  },

  methods: {
    async fetchProtocol() {
      // 知情告知书，个人信息共享授权书，通用授权书
      this.agreementList = await getAgreements('hrqb-apply')
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickProductItem(product) {
      this.productPopupData = product
      this.$refs.productPopup.open()
    },

    clickApply() {
      throttle(this.applyHandler)
    },

    applyHandler() {
      const selectedProducts = this.productList.filter((item) => item.uiChecked)
      if (selectedProducts.length === 0) {
        uni.showToast({
          title: '请选择产品',
          icon: 'none'
        })
        return
      }

      this.applyProduct()
    },

    getApplyParams() {
      const params = {
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      }

      params.applyProduct = this.productList.filter((item) => item.uiChecked)
      return params
    },

    async applyProduct() {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const params = this.getApplyParams()
      await applyFormProduct(params)

      uni.hideLoading()

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v120/apply/index',
        wechat: '/extreme/v120/qw/index',
        overloan: '/extreme/v120/applyOther/index',
        end: '/extreme/v120/download/index',
        halfapi: '/extreme/v120/webviewDown/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    demandAmountBlur() {
      const amount = parseInt(this.form.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = '200000'
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = amount
      this.updateAmountUI()
    },

    updateAmountUI() {
      this.form.demandAmount = formatAmount(this.form.demandAmount)
      this.computedMonthPay()
    },

    demandAmountFocus() {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    clickEditAmount() {
      this.$refs.amountInput.$el.querySelector('input').focus()
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    monthPickerChange({ detail }) {
      this.form.monthIndex = detail.value
      this.updateAmountUI()
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async getFlowData() {
      const flowData = getFlowData('offline')
      if (flowData) {
        flowData.productList.forEach((product) => {
          product.uiChecked = true
          // 随机生成匹配率（95～99）
          product.matchingDegree = Math.floor(Math.random() * 5 + 95)
        })
        this.productList = flowData.productList
        this.form.appUserId = flowData.appUserId
        this.form.channelId = flowData.channelId
      }
    }
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <Header @click.native="navigateToIndex" />

      <view class="header">
        <view class="header-title">
          <view class="header-title-text">
            <image
              class="header-title-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/2226f425a9ae44c7b9084b082c69afc7.png"
            ></image>
            <view>资方匹配成功</view>
          </view>
          <view class="header-title-subtext">请选择借款期数/借款金额</view>
        </view>
        <view class="tag">
          <view class="tag-number">1000</view>
          <view class="tag-gap-text">人</view>
          已放款
        </view>
      </view>

      <view class="card amount-input-container">
        <view class="amount-title">最高可借(元）</view>
        <view class="input-container">
          <view class="amount-symbol">￥</view>
          <input
            class="amount-input"
            type="text"
            value="20,0000"
            v-model="form.demandAmount"
            @blur="demandAmountBlur"
            @focus="demandAmountFocus"
            ref="amountInput"
          />
          <view class="edit-amount-btn" @click="clickEditAmount">修改金额</view>
        </view>
        <view class="amount-tips">该额度为授信金额，您可根据实际需求金额提现</view>
      </view>

      <view
        class="borrowing-options card"
        :class="{ activeForm: activeForm === 'borrowing' }"
        @click="setActiveForm('borrowing')"
      >
        <view style="padding: 0 30rpx 30rpx" class="tips-text">最长借多久</view>
        <view class="option term" style="padding-top: 0">
          <view class="label">申请期数</view>
          <picker
            range-key="label"
            :range="monthRange"
            :value="form.monthIndex"
            @change="monthPickerChange"
          >
            <view class="value">
              <view>{{ monthRange[this.form.monthIndex].value }}个月</view>
              <view class="recommend" v-if="monthRange[this.form.monthIndex].value === 12"
                >高通过率</view
              >
              <uni-icons
                style="margin-left: 15rpx"
                color="#A2A3A5"
                type="right"
                size="16"
              ></uni-icons>
            </view>
          </picker>
        </view>
        <view class="item-line"></view>
        <view class="option repayment-method">
          <view class="label">还款计划</view>
          <view class="value">
            <view class="repayment-amount">
              每月约应还
              <text class="amount-number">￥{{ monthlyPay }}</text>
            </view>
            <!--          <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view>-->
          </view>
        </view>
        <view class="item-line"></view>
        <view class="option coupon" style="padding-bottom: 0">
          <view class="label">年利率</view>
          <view class="value">
            <view class="annual-interest-rate">12%起</view>
            <view class="limited-time-offer">限时优惠</view>
          </view>
        </view>
        <view style="padding: 15rpx 30rpx 0rpx" class="tips-text"
          >实际贷款利息及放款金额以最终审批为准</view
        >
      </view>

      <view
        class="product-container card"
        :class="{ activeForm: activeForm === 'product' }"
        @click="setActiveForm('product')"
      >
        <view class="product-count"
          >已匹配
          <view class="product-count-length">{{ productList.length }}</view>
          家机构，通过率预估
          <view class="highlight">98%</view>
        </view>
        <view class="product-list">
          <view v-for="(product, index) in productList" :key="index">
            <view class="product-list-item" @click.stop="clickProductItem(product)">
              <view class="product-info">
                <image :src="product.logo" class="product-icon" />
                <view class="product-info">
                  <view class="product-name">{{ product.productName }}</view>
                  <view class="product-line-col"></view>
                  <view class="product-matching-rate">{{ product.matchingDegree }}%</view>
                  <image
                    class="product-expand"
                    src="https://cdn.oss-unos.hmctec.cn/common/path/1a505b0f69f9418ea6268d79f25720da.png"
                  />
                </view>
              </view>
              <view class="product-checkbox">
                <image
                  src="https://cdn.oss-unos.hmctec.cn/common/path/51c0e0ac809440d4b2c1a4a12cb6ca49.png"
                  class="product-checkbox-item"
                  v-if="product.uiChecked"
                  @click.stop="product.uiChecked = false"
                />
                <image
                  src="https://cdn.oss-unos.hmctec.cn/common/path/7ad8210f35c847b6bb0862216d131e52.png"
                  class="product-checkbox-item"
                  v-else
                  @click.stop="product.uiChecked = true"
                />
                <view class="speech-bubble" v-if="!product.uiChecked">
                  <image
                    class="icon-good"
                    src="https://cdn.oss-unos.hmctec.cn/common/path/99a4c6f68344488eb6c0f7c925230101.png"
                  />
                  通过率+3%
                </view>
              </view>
            </view>

            <view class="item-line" v-if="index !== productList.length - 1"></view>
          </view>
        </view>
      </view>

      <Declaration />
    </view>
    <view class="page-footer">
      <view class="agreement">
        <view class="agreement-text">
          我已阅读并同意
          <text
            class="name"
            v-for="item in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item)"
          >
            《{{ item.name }}》
          </text>
        </view>
      </view>
      <view class="apply-btn" @click="clickApply"> 确认申请 </view>
    </view>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickApply">同意并继续</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup
      background-color="#fff"
      ref="productPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="product-popup">
        <view class="institution">机构信息</view>
        <view class="product">
          <view class="product-content">
            <view class="product-info">
              <image :src="productPopupData.logo" class="product-icon" />
              <view class="product-name">{{ productPopupData.productName }}</view>
              <image
                src="https://cdn.oss-unos.hmctec.cn/common/path/f6d8666f3ba241b1ba3386b6e84a2cda.png"
                class="icon-hot"
              />
              <image
                src="https://cdn.oss-unos.hmctec.cn/common/path/1eda8b37a4724dd3a688ae4b11727e8c.png"
                class="icon-diamond-selection"
              />
            </view>
            <view class="product-feature">
              <view class="product-feature-item">
                <view class="product-feature-value highlight">{{
                  productPopupData.loanableFundsBig | toThousandFilter
                }}</view>
                <view class="product-feature-label">最高额度(元)</view>
              </view>
              <view class="product-feature-item">
                <view class="product-feature-value">{{ productPopupData.putinPush }}</view>
                <view class="product-feature-label">放款人数</view>
              </view>
            </view>
          </view>
          <view class="product-checkbox">
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/51c0e0ac809440d4b2c1a4a12cb6ca49.png"
              class="product-checkbox-item"
              v-if="productPopupData.uiChecked"
              @click.stop="productPopupData.uiChecked = false"
            />
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/9d699c296cab4436a0b77b7edffc7e5a.png"
              class="product-checkbox-item"
              v-else
              @click.stop="productPopupData.uiChecked = true"
            />
            <view class="speech-bubble" v-if="!productPopupData.uiChecked">
              <image
                class="icon-good"
                src="https://cdn.oss-unos.hmctec.cn/common/path/83c1fe9159764a77b8a0a46017422232.png"
              />
              通过率+3%
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<style scoped lang="scss">
$color-primary: #2a6af7;

.color-primary {
  color: $color-primary;
}
.page-container {
  height: 100vh;
  background: #f6f6f8;
  // display: flex;
  // flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden auto;
    position: relative;
    padding-bottom: 400rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/87371021ec1a4ef7842472d71b3e1857.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.302);
    border-radius: 8rpx 8rpx 0rpx 0rpx;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .apply-btn {
      margin-top: 20rpx;
      padding: 21rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 56rpx;
      text-align: center;
    }

    .agreement {
      display: flex;
      gap: 5rpx;

      .agree-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;
        //text-align: center;

        .name {
          color: $color-primary;
        }
      }
    }
  }
}

.header {
  position: relative;
  padding: 0rpx 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
      display: flex;
      align-items: center;
      gap: 10rpx;

      .header-title-icon {
        width: 38rpx;
        height: 38rpx;
      }
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 50rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}

.amount-input-container {
  padding: 30rpx !important;

  .amount-title {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 35rpx;
  }

  .input-container {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #cdcdcd;

    .amount-symbol {
      font-weight: 700;
      font-size: 48rpx;
      color: #333333;
      line-height: 63rpx;
    }

    .amount-input {
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 106rpx;
      font-family: DIN;
    }

    .edit-amount-btn {
      flex-shrink: 0;
      background: $color-primary;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #eef9f3;
      line-height: 22rpx;
      text-align: center;
      padding: 15rpx 20rpx;
    }
  }

  .amount-tips {
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 35rpx;
  }
}

.card {
  position: relative;
  padding: 30rpx 0;
  margin: 0 30rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }
}

.tips-text {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  line-height: 29rpx;
}

.item-line {
  margin-left: 30rpx;
  height: 2rpx;
  background-color: #f6f6f6;
}

.borrowing-options {
  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx 203rpx 203rpx 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 12rpx;
      display: flex;
      align-items: center;

      .annual-interest-rate {
        font-weight: 400;
        font-size: 28rpx;
        color: #ffc93b;
        line-height: 32rpx;
      }

      .limited-time-offer {
        padding: 5rpx 7rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #ffffff;
        line-height: 23rpx;
        background: linear-gradient(90deg, #ff8b5f 0%, #ffba61 55%, #ff8058 100%);
        border-radius: 4rpx 4rpx 4rpx 4rpx;
      }
    }
  }
}

.product-container {
  .product-count {
    display: flex;
    align-items: center;
    gap: 5rpx;
    padding: 0 30rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #a2a3a5;
    line-height: 35rpx;

    .highlight {
      color: #f01515;
    }
  }

  .product-list {
    .product-list-item {
      padding: 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .product-info {
        display: flex;
        align-items: center;

        .product-icon {
          margin-right: 12rpx;
          width: 32rpx;
          height: 32rpx;
        }

        .product-name {
          font-weight: 400;
          font-size: 28rpx;
          color: #3d3d3d;
          line-height: 41rpx;
        }

        .product-line-col {
          margin: 0 12rpx;
          width: 2rpx;
          height: 31rpx;
          background-color: #d8d8d8;
        }

        .product-matching-rate {
          margin: 14rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #ffc93b;
          line-height: 35rpx;
        }

        .product-expand {
          width: 16rpx;
          height: 16rpx;
        }
      }

      .product-checkbox {
        position: relative;
        width: 30rpx;
        height: 30rpx;

        .speech-bubble {
          width: 160rpx;
          height: 50rpx;
          display: flex;
          align-items: center;
          gap: 5rpx;
          position: absolute;
          top: -50rpx;
          right: -30rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ffffff;
          line-height: 29rpx;
          background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/c8872af589644b15999e088c0121158e.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          padding: 10rpx 15rpx 20rpx;

          .icon-good {
            width: 20rpx;
            height: 20rpx;
          }
        }

        .product-checkbox-item {
          width: 30rpx;
          height: 30rpx;
        }
      }
    }
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/a2b94098ce43424aa6d2b4b722c36c65.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}

.product-popup {
  background: linear-gradient(180deg, #d1e4ff 0%, #fcfcfc 31%);
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  padding: 45rpx 25rpx 60rpx;

  .institution {
    margin-bottom: 24rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #1d283a;
    line-height: 52rpx;
  }

  .product {
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .product-content {
      .product-info {
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;

        .product-icon {
          margin-right: 15rpx;
          width: 48rpx;
          height: 48rpx;
        }

        .product-name {
          margin-right: 20rpx;
          font-weight: 400;
          font-size: 32rpx;
          color: #171a1d;
          line-height: 45rpx;
        }

        .icon-hot {
          margin-right: 15rpx;
          width: 94rpx;
          height: 34rpx;
        }

        .icon-diamond-selection {
          width: 123rpx;
          height: 36rpx;
        }
      }

      .product-feature {
        display: flex;
        align-items: center;
        gap: 70rpx;

        .product-feature-item {
          .product-feature-value {
            margin-bottom: 8rpx;
            font-family: D-DIN, D-DIN;
            font-weight: 700;
            font-size: 38rpx;
            color: #171a1d;
            line-height: 41rpx;
          }

          .product-feature-label {
            font-weight: normal;
            font-size: 24rpx;
            color: #919094;
            line-height: 34rpx;
          }

          .highlight {
            color: #f55c4d;
          }
        }
      }
    }

    .product-checkbox {
      position: relative;
      width: 30rpx;
      height: 30rpx;

      .speech-bubble {
        width: 160rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
        gap: 5rpx;
        position: absolute;
        top: -50rpx;
        right: -30rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #ffffff;
        line-height: 29rpx;
        background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/c8872af589644b15999e088c0121158e.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 10rpx 15rpx 20rpx;

        .icon-good {
          width: 20rpx;
          height: 20rpx;
        }
      }

      .product-checkbox-item {
        width: 30rpx;
        height: 30rpx;
      }
    }
  }
}
</style>
